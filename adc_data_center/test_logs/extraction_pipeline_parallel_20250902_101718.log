2025-09-02 10:17:18 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:86] - Logging initialized. Log file: test_logs/extraction_pipeline_parallel_20250902_101718.log
2025-09-02 10:17:18 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:30] - Testing multiple ADC processing with file: output/W2886837533.md
2025-09-02 10:17:18 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:573] - Building parallel extraction pipeline
2025-09-02 10:17:18 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:271] - Extracting ADCs
2025-09-02 10:17:28 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:250] - Loading 3 ADC names into memory
2025-09-02 10:17:28 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:254] - Loaded ADC name: GSK2857916
2025-09-02 10:17:28 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:254] - Loaded ADC name: HDP-101
2025-09-02 10:17:28 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:254] - Loaded ADC name: MEDI2228
2025-09-02 10:17:45 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:280] - Extracted 3 ADCs
2025-09-02 10:17:45 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:291] - Extracting all models from study as a free form text
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:261] - Loading 24 model names into memory for ADC: BCMA-targeted ADCs (GSK2857916, HDP-101, MEDI2228)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Patient-derived BCMA+ multiple myeloma cells (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: BCMA-negative normal cells (used as negative control in GSK2857916 cytotoxicity assays)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: BCMA-negative cells (used as negative control in HDP-101 cytotoxicity assays)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Cynomolgus monkeys (used for tolerability and pharmacokinetics of HDP-101)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Eight of ten multiple myeloma cell lines with varying BCMA levels (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Bone marrow stromal cells (BMSCs) (used in co-culture with MM cell lines for MEDI2228 activity)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Recombinant monomeric human BCMA protein (used for binding assays with MEDI2228)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: NCI-H929 subcutaneous xenograft model in mice (used for in vivo efficacy of BI 836909 bispecific T-cell engager)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Orthotopic L-363 xenograft model in mice (used for in vivo efficacy of BI 836909 bispecific T-cell engager)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Cynomolgus monkeys (used for depletion of BCMA+ plasma cells in bone marrow after BI 836909 administration)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: BM stromal cells (used in co-culture with MM cells for BI 836909 activity)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Soluble APRIL and BCMA (used as additives in in vitro anti-MM activity assays with BI 836909)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: MM patient samples (used for cytotoxicity assays with fully human IgG CD3 bispecific molecule targeting BCMA)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: BM samples of previously untreated and RRMM patients (used for EM801 bispecific antibody cytotoxicity assays)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: NK cells (used for cytotoxicity assays with AFM26 bispecific antibody)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Mice (used for in vivo anti-MM cytotoxicity assays with TNB383B and TNB-384B bispecific antibodies)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Human MM cell lines (used for APRIL-induced proliferation and anti-APRIL antibody hAPRIL01A inhibition assays)
2025-09-02 10:18:08 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Cocultures of MM cells with BCMA-negative bone marrow accessory cells and effector cells (used for hAPRIL01A activity assays)
2025-09-02 10:18:28 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:299] - Extracted all models: ['1. CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)', '2. Patient-derived BCMA+ multiple myeloma cells (used for GSK2857916 binding and cytotoxicity assays)', '3. BCMA-negative normal cells (used as negative control in GSK2857916 cytotoxicity assays)', '4. Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)', '5. Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)', '6. BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)', '7. BCMA-negative cells (used as negative control in HDP-101 cytotoxicity assays)', '8. Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)', '9. Cynomolgus monkeys (used for tolerability and pharmacokinetics of HDP-101)', '10. Eight of ten multiple myeloma cell lines with varying BCMA levels (used for in vitro cytotoxicity assays with MEDI2228)', '11. Bone marrow stromal cells (BMSCs) (used in co-culture with MM cell lines for MEDI2228 activity)', '12. Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228)', '13. Recombinant monomeric human BCMA protein (used for binding assays with MEDI2228)', '14. NCI-H929 subcutaneous xenograft model in mice (used for in vivo efficacy of BI 836909 bispecific T-cell engager)', '15. Orthotopic L-363 xenograft model in mice (used for in vivo efficacy of BI 836909 bispecific T-cell engager)', '16. Cynomolgus monkeys (used for depletion of BCMA+ plasma cells in bone marrow after BI 836909 administration)', '17. BM stromal cells (used in co-culture with MM cells for BI 836909 activity)', '18. Soluble APRIL and BCMA (used as additives in in vitro anti-MM activity assays with BI 836909)', '19. MM patient samples (used for cytotoxicity assays with fully human IgG CD3 bispecific molecule targeting BCMA)', '20. BM samples of previously untreated and RRMM patients (used for EM801 bispecific antibody cytotoxicity assays)', '21. NK cells (used for cytotoxicity assays with AFM26 bispecific antibody)', '22. Mice (used for in vivo anti-MM cytotoxicity assays with TNB383B and TNB-384B bispecific antibodies)', '23. Human MM cell lines (used for APRIL-induced proliferation and anti-APRIL antibody hAPRIL01A inhibition assays)', '24. Cocultures of MM cells with BCMA-negative bone marrow accessory cells and effector cells (used for hAPRIL01A activity assays)']
2025-09-02 10:18:28 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:327] - Extracting models for ADC: GSK2857916
2025-09-02 10:18:28 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:327] - Extracting models for ADC: HDP-101
2025-09-02 10:18:28 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:327] - Extracting models for ADC: MEDI2228
2025-09-02 10:18:39 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:261] - Loading 5 model names into memory for ADC: GSK2857916
2025-09-02 10:18:39 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:18:39 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Patient-derived BCMA+ multiple myeloma cells (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:18:39 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: BCMA-negative normal cells (used as negative control in GSK2857916 cytotoxicity assays)
2025-09-02 10:18:39 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:18:39 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:18:43 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:261] - Loading 4 model names into memory for ADC: HDP-101
2025-09-02 10:18:43 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:18:43 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: BCMA-negative cell lines (used as negative control in in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:18:43 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)
2025-09-02 10:18:43 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)
2025-09-02 10:18:44 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:261] - Loading 5 model names into memory for ADC: MEDI2228
2025-09-02 10:18:44 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:18:44 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Co-culture of human multiple myeloma cell lines with bone marrow stromal cells (BMSCs) (used for in vitro activity assays with MEDI2228)
2025-09-02 10:18:44 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)
2025-09-02 10:18:44 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Recombinant monomeric human BCMA protein (used for binding assays with MEDI2228)
2025-09-02 10:18:44 - extraction_pipeline_parallel_strurcture - DEBUG - [extraction_pipeline_parallel_strurcture.py:265] - Loaded model name: Human multiple myeloma cell lines with soluble BCMA (sBCMA) additive (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:18:51 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:342] - Extracted 5 models for ADC: GSK2857916
2025-09-02 10:18:51 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:358] - Skipping clinical model: Patient-derived BCMA+ multiple myeloma cells (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:18:58 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:342] - Extracted 4 models for ADC: HDP-101
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:342] - Extracted 5 models for ADC: MEDI2228
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: GSK2857916, Model: CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: GSK2857916, Model: BCMA-negative normal cells (used as negative control in GSK2857916 cytotoxicity assays)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: GSK2857916, Model: Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: GSK2857916, Model: Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: HDP-101, Model: BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: HDP-101, Model: BCMA-negative cell lines (used as negative control in in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: HDP-101, Model: Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: HDP-101, Model: Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: MEDI2228, Model: Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: MEDI2228, Model: Co-culture of human multiple myeloma cell lines with bone marrow stromal cells (BMSCs) (used for in vitro activity assays with MEDI2228)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: MEDI2228, Model: Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: MEDI2228, Model: Recombinant monomeric human BCMA protein (used for binding assays with MEDI2228)
2025-09-02 10:19:05 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:379] - Extracting available endpoints for ADC: MEDI2228, Model: Human multiple myeloma cell lines with soluble BCMA (sBCMA) additive (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:19:39 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 2 endpoints are available for ADC: GSK2857916, Model: Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:19:41 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 1 endpoints are available for ADC: GSK2857916, Model: BCMA-negative normal cells (used as negative control in GSK2857916 cytotoxicity assays)
2025-09-02 10:19:59 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 2 endpoints are available for ADC: HDP-101, Model: BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:20:23 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 1 endpoints are available for ADC: HDP-101, Model: BCMA-negative cell lines (used as negative control in in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:20:40 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 3 endpoints are available for ADC: HDP-101, Model: Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)
2025-09-02 10:20:57 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 2 endpoints are available for ADC: GSK2857916, Model: CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:21:10 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 2 endpoints are available for ADC: GSK2857916, Model: Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:21:23 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 3 endpoints are available for ADC: MEDI2228, Model: Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:21:43 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 0 endpoints are available for ADC: MEDI2228, Model: Recombinant monomeric human BCMA protein (used for binding assays with MEDI2228)
2025-09-02 10:21:45 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 5 endpoints are available for ADC: HDP-101, Model: Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)
2025-09-02 10:21:51 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 1 endpoints are available for ADC: MEDI2228, Model: Co-culture of human multiple myeloma cell lines with bone marrow stromal cells (BMSCs) (used for in vitro activity assays with MEDI2228)
2025-09-02 10:22:04 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 3 endpoints are available for ADC: MEDI2228, Model: Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:407] - 1 endpoints are available for ADC: MEDI2228, Model: Human multiple myeloma cell lines with soluble BCMA (sBCMA) additive (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: GSK2857916, Models: ['CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)', 'BCMA-negative normal cells (used as negative control in GSK2857916 cytotoxicity assays)'], Endpoint: ANTIGEN_EXPRESSION
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: GSK2857916, Models: ['CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)'], Endpoint: ADC_KD
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: GSK2857916, Models: ['Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)', 'Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)'], Endpoint: ANTI_TUMOR_ACTIVITY_DOSE
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: GSK2857916, Models: ['Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)', 'Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)'], Endpoint: TUMOR_GROWTH_INHIBITION
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)', 'BCMA-negative cell lines (used as negative control in in vitro cytotoxicity assays with HDP-101)'], Endpoint: ANTIGEN_EXPRESSION
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)'], Endpoint: ADC_IC50
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)'], Endpoint: ANTI_TUMOR_ACTIVITY_DOSE
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)'], Endpoint: TUMOR_GROWTH_INHIBITION
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)'], Endpoint: OBJECTIVE_RESPONSE_RATE
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: PK_DOSE
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: ADC_T_HALF
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: TOXICOLOGY_DOSE
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: TOXICITY_DOSING_REGIMEN
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: INCREASED_AST
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: MEDI2228, Models: ['Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)'], Endpoint: ANTIGEN_EXPRESSION
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: MEDI2228, Models: ['Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)'], Endpoint: ADC_INTERNALIZATION
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: MEDI2228, Models: ['Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)', 'Co-culture of human multiple myeloma cell lines with bone marrow stromal cells (BMSCs) (used for in vitro activity assays with MEDI2228)', 'Human multiple myeloma cell lines with soluble BCMA (sBCMA) additive (used for in vitro cytotoxicity assays with MEDI2228)'], Endpoint: ADC_IC50
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: MEDI2228, Models: ['Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)'], Endpoint: ADC_TREATMENT_CONCENTRATION
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: MEDI2228, Models: ['Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)'], Endpoint: ANTI_TUMOR_ACTIVITY_DOSE
2025-09-02 10:22:27 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:494] - Extracting endpoints for ADC: MEDI2228, Models: ['Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)'], Endpoint: TUMOR_GROWTH_INHIBITION
2025-09-02 10:22:40 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: GSK2857916, Models: ['CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)'], Endpoint: ADC_KD
2025-09-02 10:22:40 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: GSK2857916, Models: ['CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)', 'BCMA-negative normal cells (used as negative control in GSK2857916 cytotoxicity assays)'], Endpoint: ANTIGEN_EXPRESSION
2025-09-02 10:22:43 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: GSK2857916, Models: ['Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)', 'Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)'], Endpoint: ANTI_TUMOR_ACTIVITY_DOSE
2025-09-02 10:22:43 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)', 'BCMA-negative cell lines (used as negative control in in vitro cytotoxicity assays with HDP-101)'], Endpoint: ANTIGEN_EXPRESSION
2025-09-02 10:22:48 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: GSK2857916, Models: ['Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)', 'Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)'], Endpoint: TUMOR_GROWTH_INHIBITION
2025-09-02 10:22:49 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)'], Endpoint: ADC_IC50
2025-09-02 10:22:54 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: PK_DOSE
2025-09-02 10:22:55 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)'], Endpoint: OBJECTIVE_RESPONSE_RATE
2025-09-02 10:22:56 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)'], Endpoint: ANTI_TUMOR_ACTIVITY_DOSE
2025-09-02 10:23:32 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: ADC_T_HALF
2025-09-02 10:23:38 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: TOXICITY_DOSING_REGIMEN
2025-09-02 10:23:41 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: MEDI2228, Models: ['Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)'], Endpoint: ANTIGEN_EXPRESSION
2025-09-02 10:23:46 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: MEDI2228, Models: ['Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)'], Endpoint: ADC_INTERNALIZATION
2025-09-02 10:23:47 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: MEDI2228, Models: ['Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)', 'Co-culture of human multiple myeloma cell lines with bone marrow stromal cells (BMSCs) (used for in vitro activity assays with MEDI2228)', 'Human multiple myeloma cell lines with soluble BCMA (sBCMA) additive (used for in vitro cytotoxicity assays with MEDI2228)'], Endpoint: ADC_IC50
2025-09-02 10:23:48 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: MEDI2228, Models: ['Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)'], Endpoint: ANTI_TUMOR_ACTIVITY_DOSE
2025-09-02 10:23:55 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: MEDI2228, Models: ['Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)'], Endpoint: TUMOR_GROWTH_INHIBITION
2025-09-02 10:24:08 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)'], Endpoint: TUMOR_GROWTH_INHIBITION
2025-09-02 10:24:12 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: INCREASED_AST
2025-09-02 10:24:34 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: MEDI2228, Models: ['Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)'], Endpoint: ADC_TREATMENT_CONCENTRATION
2025-09-02 10:24:37 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:515] - Successfully extracted endpoint measurement for ADC: HDP-101, Models: ['Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)'], Endpoint: TOXICOLOGY_DOSE
2025-09-02 10:24:37 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:526] - Discarding unnecessary endpoint measurements
2025-09-02 10:24:42 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC GSK2857916, Endpoint ANTIGEN_EXPRESSION is NOT valid and will NOT be saved.
2025-09-02 10:24:45 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC GSK2857916, Endpoint ANTIGEN_EXPRESSION is NOT valid and will NOT be saved.
2025-09-02 10:24:48 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC GSK2857916, Endpoint ADC_KD is valid and will be saved.
2025-09-02 10:24:51 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC GSK2857916, Endpoint ANTI_TUMOR_ACTIVITY_DOSE is valid and will be saved.
2025-09-02 10:24:53 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC GSK2857916, Endpoint ANTI_TUMOR_ACTIVITY_DOSE is valid and will be saved.
2025-09-02 10:24:57 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC GSK2857916, Endpoint TUMOR_GROWTH_INHIBITION is valid and will be saved.
2025-09-02 10:25:00 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC GSK2857916, Endpoint TUMOR_GROWTH_INHIBITION is valid and will be saved.
2025-09-02 10:25:04 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC HDP-101, Endpoint ANTIGEN_EXPRESSION is NOT valid and will NOT be saved.
2025-09-02 10:25:07 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC HDP-101, Endpoint ANTIGEN_EXPRESSION is valid and will be saved.
2025-09-02 10:25:09 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC HDP-101, Endpoint ADC_IC50 is NOT valid and will NOT be saved.
2025-09-02 10:25:11 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC HDP-101, Endpoint ANTI_TUMOR_ACTIVITY_DOSE is NOT valid and will NOT be saved.
2025-09-02 10:25:14 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC HDP-101, Endpoint TUMOR_GROWTH_INHIBITION is NOT valid and will NOT be saved.
2025-09-02 10:25:18 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC HDP-101, Endpoint OBJECTIVE_RESPONSE_RATE is NOT valid and will NOT be saved.
2025-09-02 10:25:26 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC HDP-101, Endpoint PK_DOSE is valid and will be saved.
2025-09-02 10:25:28 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC HDP-101, Endpoint ADC_T_HALF is valid and will be saved.
2025-09-02 10:25:34 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC HDP-101, Endpoint TOXICOLOGY_DOSE is NOT valid and will NOT be saved.
2025-09-02 10:25:39 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC HDP-101, Endpoint TOXICITY_DOSING_REGIMEN is NOT valid and will NOT be saved.
2025-09-02 10:25:44 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC HDP-101, Endpoint INCREASED_AST is valid and will be saved.
2025-09-02 10:25:48 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC MEDI2228, Endpoint ANTIGEN_EXPRESSION is NOT valid and will NOT be saved.
2025-09-02 10:25:50 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC MEDI2228, Endpoint ADC_INTERNALIZATION is valid and will be saved.
2025-09-02 10:25:53 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC MEDI2228, Endpoint ADC_IC50 is valid and will be saved.
2025-09-02 10:25:58 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC MEDI2228, Endpoint ADC_IC50 is NOT valid and will NOT be saved.
2025-09-02 10:26:01 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC MEDI2228, Endpoint ADC_TREATMENT_CONCENTRATION is valid and will be saved.
2025-09-02 10:26:03 - extraction_pipeline_parallel_strurcture - INFO - [extraction_pipeline_parallel_strurcture.py:558] - Measurement for ADC MEDI2228, Endpoint ANTI_TUMOR_ACTIVITY_DOSE is valid and will be saved.
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - WARNING - [extraction_pipeline_parallel_strurcture.py:560] - Measurement for ADC MEDI2228, Endpoint TUMOR_GROWTH_INHIBITION is NOT valid and will NOT be saved.
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:37] - Extraction completed successfully!
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:38] - Found 3 ADCs
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:39] - Found 14 models
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:40] - Found 11 endpoints
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:45] - ADC 1: GSK2857916 (Type: AntibodyDrugConjugateType.INVESTIGATIVE)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:45] - ADC 2: HDP-101 (Type: AntibodyDrugConjugateType.INVESTIGATIVE)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:45] - ADC 3: MEDI2228 (Type: AntibodyDrugConjugateType.INVESTIGATIVE)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:57] - ADC 'GSK2857916' has 5 models:
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - CD138+ and BCMA+ multiple myeloma (MM) cell lines (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Patient-derived BCMA+ multiple myeloma cells (used for GSK2857916 binding and cytotoxicity assays)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - BCMA-negative normal cells (used as negative control in GSK2857916 cytotoxicity assays)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Disseminated human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Subcutaneous human multiple myeloma xenograft model in mice (used for in vivo efficacy of GSK2857916)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:57] - ADC 'HDP-101' has 4 models:
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - BCMA-expressing multiple myeloma cell lines (used for in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - BCMA-negative cell lines (used as negative control in in vitro cytotoxicity assays with HDP-101)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Mouse xenograft model with human multiple myeloma cells (used for in vivo efficacy of HDP-101)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Cynomolgus monkeys (used for tolerability, safety, and pharmacokinetics studies with HDP-101)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:57] - ADC 'MEDI2228' has 5 models:
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Human multiple myeloma cell lines with varying BCMA expression (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Co-culture of human multiple myeloma cell lines with bone marrow stromal cells (BMSCs) (used for in vitro activity assays with MEDI2228)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Human multiple myeloma xenograft model in mice (used for in vivo efficacy of MEDI2228 at 0.1 mg/kg dosing)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Recombinant monomeric human BCMA protein (used for binding assays with MEDI2228)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:59] -   - Human multiple myeloma cell lines with soluble BCMA (sBCMA) additive (used for in vitro cytotoxicity assays with MEDI2228)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:70] - ADC 'GSK2857916' has 3 endpoints:
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - ADC_KD (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - ANTI_TUMOR_ACTIVITY_DOSE (2 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - TUMOR_GROWTH_INHIBITION (2 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:70] - ADC 'HDP-101' has 4 endpoints:
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - ANTIGEN_EXPRESSION (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - PK_DOSE (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - ADC_T_HALF (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - INCREASED_AST (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:70] - ADC 'MEDI2228' has 4 endpoints:
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - ADC_INTERNALIZATION (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - ADC_IC50 (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - ADC_TREATMENT_CONCENTRATION (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:72] -   - ANTI_TUMOR_ACTIVITY_DOSE (1 measurements)
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:78] - Investigative ADCs: 3
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:79] - ADCs with endpoints: 3
2025-09-02 10:26:13 - extraction_pipeline_parallel_strurcture - INFO - [test_multiple_adcs.py:83] - ✅ SUCCESS: Multiple ADCs processed successfully!
