#!/usr/bin/env python3
"""
<PERSON>ript to clean up problematic ADC-level records from extraction results JSON files.

This script identifies and removes ADC-level records that interfere with endpoint-level evaluation.
These records have the pattern:
{
    "id": null,
    "model_name": null,
    "endpoint_name": null,
    "priority": "prioritize"
    // adc_name can be any value (not null)
}

The script processes all JSON files in the extraction_results directory.
"""

import json
import os
import sys
from pathlib import Path
from typing import List, Dict, Any, <PERSON><PERSON>


def has_problematic_null_pattern(record: Dict[str, Any]) -> bool:
    """
    Check if a record contains the problematic null field pattern.

    Returns True if the record is an ADC-level record (not endpoint-level) with:
    - "id": null
    - "model_name": null
    - "endpoint_name": null
    - "priority": "prioritize"

    These are ADC-level records that interfere with endpoint-level evaluation.
    """
    # Check for the ADC-level record pattern (these should be removed for endpoint evaluation)
    return (
        record.get("id") is None and
        record.get("model_name") is None and
        record.get("endpoint_name") is None and
        record.get("priority") == "prioritize"
    )


def clean_json_file(file_path: Path) -> Tuple[int, int, bool]:
    """
    Clean a single JSON file by removing problematic null records.
    
    Returns:
        Tuple of (original_count, cleaned_count, success)
    """
    try:
        # Load the JSON file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        original_count = 0
        cleaned_data = None
        
        if isinstance(data, list):
            # Handle array of records
            original_count = len(data)
            cleaned_data = [record for record in data if not has_problematic_null_pattern(record)]
        elif isinstance(data, dict):
            # Handle single record
            original_count = 1
            if not has_problematic_null_pattern(data):
                cleaned_data = data
            else:
                cleaned_data = {}  # Empty dict if the single record is problematic
        else:
            # Unexpected data type
            print(f"Warning: Unexpected data type in {file_path}: {type(data)}")
            return 0, 0, False
        
        cleaned_count = len(cleaned_data) if isinstance(cleaned_data, list) else (1 if cleaned_data else 0)
        
        # Only write back if there were changes
        if original_count != cleaned_count:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, indent=2, ensure_ascii=False)
            print(f"✓ Cleaned {file_path.name}: {original_count} → {cleaned_count} records ({original_count - cleaned_count} removed)")
        else:
            print(f"  No changes needed for {file_path.name}: {original_count} records")
        
        return original_count, cleaned_count, True
        
    except json.JSONDecodeError as e:
        print(f"✗ JSON decode error in {file_path}: {e}")
        return 0, 0, False
    except Exception as e:
        print(f"✗ Error processing {file_path}: {e}")
        return 0, 0, False


def main():
    """Main function to process all JSON files in the extraction_results directory."""
    
    # Get the extraction results directory
    script_dir = Path(__file__).parent
    extraction_results_dir = script_dir / "data" / "extraction_results"
    
    if not extraction_results_dir.exists():
        print(f"Error: Directory not found: {extraction_results_dir}")
        sys.exit(1)
    
    # Find all JSON files (excluding normalization_log.json)
    json_files = [f for f in extraction_results_dir.glob("*.json") 
                  if f.name != "normalization_log.json"]
    
    if not json_files:
        print("No JSON files found in the extraction_results directory.")
        return
    
    print(f"Found {len(json_files)} JSON files to process...")
    print("=" * 60)
    
    total_original = 0
    total_cleaned = 0
    successful_files = 0
    failed_files = 0
    
    # Process each file
    for json_file in sorted(json_files):
        original_count, cleaned_count, success = clean_json_file(json_file)
        
        total_original += original_count
        total_cleaned += cleaned_count
        
        if success:
            successful_files += 1
        else:
            failed_files += 1
    
    # Print summary
    print("=" * 60)
    print("CLEANUP SUMMARY:")
    print(f"Files processed successfully: {successful_files}")
    print(f"Files with errors: {failed_files}")
    print(f"Total records before cleanup: {total_original}")
    print(f"Total records after cleanup: {total_cleaned}")
    print(f"Total records removed: {total_original - total_cleaned}")
    
    if failed_files > 0:
        print(f"\nWarning: {failed_files} files had errors and were not processed.")
        sys.exit(1)
    else:
        print("\n✓ All files processed successfully!")


if __name__ == "__main__":
    main()
